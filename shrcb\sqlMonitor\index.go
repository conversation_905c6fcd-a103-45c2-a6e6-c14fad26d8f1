package sqlmonitor

import (
	"fmt"

	"github.com/gin-gonic/gin"
)

func StartMonitor() {
	engine := gin.Default()

	engine.POST("/api/v1/task/exec", CreateTaskExec)

	_ = engine.Run(":1010")

}

func QueryTaskExec(ctx *gin.Context) {

	var params SearchParams
	ctx.ShouldBind(&params)

	var response Response

	response.Success = true
	response.Message = "创建成功"
	response.Total = 100
	response.Data = []TaskExec{}

	ctx.JSON(200, response)

}

func CreateTaskExec(ctx *gin.Context) {

	var params TaskExec
	ctx.ShouldBind(&params)

	fmt.Println(params)

	var response Response

	response.Success = true
	response.Message = "创建成功"
	response.Total = 100
	response.Data = []TaskExec{}

	ctx.JSON(200, response)

}
