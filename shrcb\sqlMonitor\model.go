package sqlmonitor

import (
	"gorm.io/gorm"
)

type Response struct {
	Data    []TaskExec `json:"data"`
	Total   int        `json:"total"`
	Success bool       `json:"success"`
	Message string     `json:"message"`
}

type SearchParams struct {
	Current  int `json:"current"`
	PageSize int `json:"page_size"`
}

// 执行任务表
type TaskExec struct {
	gorm.Model
	Name           string   `json:"name"`             // 任务名称
	Group          string   `json:"group"`            // 任务分组
	Status         string   `json:"status"`           // 任务状态
	Start_time     string   `json:"start_time"`       // 开始时间
	EndTime        string   `json:"end_time"`         // 结束时间
	Weekday        []string `json:"weekday"`          // 执行星期
	Frequency      string   `json:"frequency"`        // 批次执行频率
	RetryNum       string   `json:"retry_num"`        // 重试次数
	RetryFrequency string   `json:"retry_frequency"`  // 重试间隔
	AlertTaskId    []string `json:"alert_task_id"`    // 告警任务ID, 一个执行任务可以有多个告警任务
	AlertSendId    []string `json:"alert_send_id"`    // 告警发送方式ID, 一个执行任务可以有多个告警发送方式
	DBConnectionId string   `json:"db_connection_id"` // db 连接id
	OtherInfoId    string   `json:"other_info_id"`    // 附加信息id
	CreateAt       string   `json:"create_time"`      // 创建时间
	UpdateAt       string   `json:"update_time"`      // 更新时间
}

func (TaskExec) TableName() string {
	return "task_exec"
}
